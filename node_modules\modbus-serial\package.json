{"name": "modbus-serial", "version": "8.0.21-no-serial-port", "description": "A pure JavaScript implemetation of MODBUS-RTU (Serial and TCP) for NodeJS.", "main": "index.js", "scripts": {"test": "nyc --reporter=lcov --reporter=text mocha --recursive"}, "repository": {"type": "git", "url": "git+https://github.com/yaacov/node-modbus-serial.git"}, "keywords": ["modbus", "rtu", "serial", "port", "com", "a<PERSON><PERSON><PERSON>"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/yaacov/node-modbus-serial/issues"}, "homepage": "https://github.com/yaacov/node-modbus-serial#readme", "devDependencies": {"chai": "^4.2.0", "chai-as-promised": "^7.1.1", "eslint": "^8.12.0", "gulp": "^4.0.2", "gulp-clean": "^0.4.0", "gulp-jsdoc3": "^3.0.0", "mocha": "^10.2.0", "mocha-eslint": "^7.0.0", "mockery": "^2.1.0", "nyc": "^15.1.0", "pump": "^3.0.0", "sinon": "^15.2.0", "web-bluetooth-mock": "^1.2.0", "webbluetooth": "^3.1.0"}, "dependencies": {"debug": "^4.3.1"}}