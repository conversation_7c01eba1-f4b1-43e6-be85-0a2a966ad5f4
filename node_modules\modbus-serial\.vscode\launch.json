{
    // Use IntelliSense to learn about possible Node.js debug attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
      {
        "name": "Debug Current File",
        "type": "node",
        "request": "launch",
        "program": "${file}"
      },
      {
        "type": "node",
        "request": "launch",
        "name": "Mocha single test",
        "program": "${workspaceFolder}/node_modules/mocha/bin/mocha.js",
        // current file
        "args": ["${file}"],
        "console": "internalConsole",
        "cwd": "${workspaceRoot}",
        "runtimeVersion": "16",
      }
    ]
  }
  