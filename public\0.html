<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modbus TCP 数据监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        body {
            background: linear-gradient(135deg, #0c1729, #1a3e75);
            min-height: 100vh;
            overflow: hidden;
            display: grid;
            grid-template-rows: auto 1fr auto;
            padding: 10px;
        }
        .status-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 15px 20px;
            margin-bottom: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }
        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        .status-connected {
            background: #4CAF50;
            box-shadow: 0 0 8px #4CAF50;
        }
        .status-disconnected {
            background: #f44336;
            box-shadow: 0 0 8px #f44336;
        }
        .status-connecting {
            background: #ff9800;
            box-shadow: 0 0 8px #ff9800;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .system-info {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        .main-content {
            display: grid;
            grid-template-rows: repeat(9, 1fr);
            gap: 10px;
        }
        .btn-container {
            display: grid;
            grid-template-columns: 1fr minmax(180px, 1fr) 1fr;
            padding: 0;
        }
        .btn {
            background: linear-gradient(145deg, #1e88e5, #0d47a1);
            color: white;
            border: none;
            border-radius: 14px;
            font-size: 1.7rem;
            font-weight: bold;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 15px rgba(0,0,0,0.4);
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 90%;
            text-decoration: none !important;
            padding: 0 10px;
            position: relative;
        }
        .btn:hover {
            transform: scale(1.03);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .btn:active {
            transform: scale(0.98);
            background: linear-gradient(145deg, #1565c0, #0d47a1);
        }
        .btn.disabled {
            background: linear-gradient(145deg, #666, #444);
            cursor: not-allowed;
            opacity: 0.6;
        }
        .info-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            padding: 10px 20px;
            margin-top: 15px;
            color: white;
            font-size: 0.85rem;
            text-align: center;
        }
        .data-info {
            display: flex;
            justify-content: space-around;
            margin-bottom: 8px;
        }
        .data-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .data-value {
            font-size: 1.1rem;
            font-weight: bold;
            color: #4CAF50;
        }
        .data-label {
            font-size: 0.75rem;
            opacity: 0.8;
        }
        @media (max-width: 768px) {
            .btn { font-size: 1.5rem; }
            .status-bar { padding: 10px 15px; }
            .info-bar { padding: 8px 15px; }
        }
        @media (max-width: 480px) {
            .btn { font-size: 1.3rem; border-radius: 12px; }
            .btn-container { grid-template-columns: 1fr minmax(150px, 1fr) 1fr; }
            .status-bar { flex-direction: column; gap: 10px; }
        }
        @media (max-height: 700px) {
            .btn { font-size: 1.4rem; }
            body { padding: 8px; }
        }
    </style>
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <!-- 顶部状态栏 -->
    <div class="status-bar">
        <div class="status-item">
            <div id="connectionStatus" class="status-indicator status-connecting"></div>
            <span id="connectionText">连接中...</span>
        </div>
        <div class="status-item" style="flex-grow: 1; display: flex; justify-content: center; align-items: center;">
            <h1 style="font-size: 1.5rem; font-weight: bold; color: #4da6ff; margin: 0; text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);">
                ModbusTCP 数据监控系统
            </h1>
        </div>
        <div class="status-item">
            <div id="plcStatus" class="status-indicator status-disconnected"></div>
            <span id="plcText">PLC未连接</span>
        </div>
    </div>
    <!-- 主要内容区域 -->
    <div class="main-content">
        <div></div><!-- 顶部间距 -->
        <!-- 第一个按钮 -->
        <div class="btn-container">
            <div></div>
            <a href="1.html" class="btn" id="btn1">24个数字量输入</a>
            <div></div>
        </div>
        <div></div><!-- 按钮间间距 -->
        <!-- 第二个按钮 -->
        <div class="btn-container">
            <div></div>
            <a href="2.html" class="btn" id="btn2">24个数字量输出</a>
            <div></div>
        </div>
        <div></div><!-- 按钮间间距 -->
        <!-- 第三个按钮 -->
        <div class="btn-container">
            <div></div>
            <a href="3.html" class="btn" id="btn3">16个模拟量输入</a>
            <div></div>
        </div>
        <div></div><!-- 按钮间间距 -->
        <!-- 第四个按钮 -->
        <div class="btn-container">
            <div></div>
            <a href="4.html" class="btn" id="btn4">16PT100输入</a>
            <div></div>
        </div>
        <div></div><!-- 底部间距 -->
    </div>
    <!-- 底部信息栏 -->
    <div class="info-bar">
        <div id="systemStatusText" style="font-size: 0.8rem; opacity: 0.7; color: #fff;">
            系统运行正常 | 数据采集正常 | WebSocket连接正常
        </div>
    </div>
    <script>
        // WebSocket连接
        const socket = io();
        let hasData = false;
        let wsConnected = false;
        let plcConnected = false;
        let lastError = null;

        // 更新系统状态栏
        function updateSystemStatus() {
            const el = document.getElementById('systemStatusText');
            const messages = [];

            if (wsConnected) {
                messages.push('服务器已连接');
            } else {
                messages.push('服务器未连接');
            }

            if (plcConnected) {
                messages.push('PLC已连接');
                if (hasData) {
                    messages.push('数据采集正常');
                } else {
                    messages.push('等待数据');
                }
            } else {
                messages.push('PLC未连接');
                if (lastError) {
                    messages.push(`错误: ${lastError}`);
                }
            }

            el.textContent = messages.join(' | ');
            
            // 设置状态颜色
            if (wsConnected && plcConnected && hasData) {
                el.style.color = '#4CAF50'; // 绿色 - 全部正常
            } else if (wsConnected && plcConnected) {
                el.style.color = '#FFA500'; // 橙色 - 连接正常但无数据
            } else if (wsConnected) {
                el.style.color = '#FF9800'; // 橙色 - 仅服务器连接
            } else {
                el.style.color = '#FF4444'; // 红色 - 完全离线
            }
        }

        // 更新WebSocket连接状态
        function updateConnectionStatus(connected) {
            wsConnected = connected;
            const statusIndicator = document.getElementById('connectionStatus');
            const statusText = document.getElementById('connectionText');
            
            if (connected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = '服务器已连接';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = '服务器未连接';
            }
            updateSystemStatus();
        }

        // 更新PLC状态
        function updatePLCStatus(status) {
            plcConnected = status.connected;
            lastError = status.error || null;
            
            const statusIndicator = document.getElementById('plcStatus');
            const statusText = document.getElementById('plcText');
            
            if (status.connected) {
                statusIndicator.className = 'status-indicator status-connected';
                statusText.textContent = 'PLC已连接';
            } else {
                statusIndicator.className = 'status-indicator status-disconnected';
                statusText.textContent = 'PLC未连接';
            }
            
            updateSystemStatus();
        }

        // 更新数据状态
        function updateDataStatus(has) {
            hasData = has;
            updateSystemStatus();
        }

        // WebSocket事件处理
        socket.on('connect', () => {
            console.log('WebSocket连接成功');
            updateConnectionStatus(true);
        });

        socket.on('disconnect', () => {
            console.log('WebSocket连接断开');
            updateConnectionStatus(false);
        });

        socket.on('plcStatus', (status) => {
            console.log('PLC状态更新:', status);
            updatePLCStatus(status);
        });

        socket.on('dataUpdate', (data) => {
            updateDataStatus(!!data);
        });

        // 初始化
        updateSystemStatus();
    </script>
</body>
</html>