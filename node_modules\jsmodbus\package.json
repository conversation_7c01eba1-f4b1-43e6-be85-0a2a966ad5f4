{"name": "jsmodbus", "version": "4.0.10", "description": "Implementation for the Serial/TCP Modbus protocol.", "author": "<PERSON> <<EMAIL>>", "main": "./dist/modbus.js", "types": "./dist/modbus.d.ts", "bin": {"jsmodbus": "./bin/jsmodbus.js"}, "repository": {"type": "git", "url": "https://github.com/Cloud-Automation/node-modbus"}, "engines": {"node": ">=6.0.0"}, "dependencies": {"crc": "3.4.0", "debug": "^3.1.0"}, "devDependencies": {"@types/crc": "^3.4.0", "@types/debug": "^4.1.4", "@types/mocha": "^5.2.7", "@types/node": "^12.0.10", "@types/serialport": "^7.0.4", "commander": "^3.0.2", "mocha": "^3.3.0", "nyc": "^13.1.0", "serialport": "^7.1.5", "sinon": "2.2.0", "tslint": "^5.20.0", "tslint-config-standard": "^8.0.1", "typescript": "^3.5.2"}, "scripts": {"test": "npm run lint && npm run build && npx mocha test/*.test.js", "build": "npx tsc", "lint": "npx tslint -c tslint.json 'src/**/*.ts'", "lint:fix": "npx tslint -c tslint.json 'src/**/*.ts' --fix", "watch": "npm run lint && npm run build && npx mocha --watch test/*.test.js", "prepublishOnly": "npm run test", "prepare": "npm run build", "cov": "nyc mocha test/*.test.js"}, "license": "MIT", "keywords": ["client", "server", "serial", "port", "modbus", "tcp"], "readmeFilename": "README.md", "directories": {"test": "test", "example": "examples"}, "files": ["bin/**/*", "dist/**/*"]}