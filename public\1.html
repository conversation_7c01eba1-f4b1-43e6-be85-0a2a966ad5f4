<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>24个数字量输入</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        body {
            background: linear-gradient(135deg, #0c1729, #1a3e75);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 8px;
        }
        .container {
            width: 100%;
            max-width: 700px;
            padding: 10px;
        }
        .input-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 8px;
            margin-bottom: 12px;
        }
        @media (max-width: 700px) {
            .input-grid { grid-template-columns: repeat(4, 1fr); }
        }
        @media (max-width: 500px) {
            .input-grid { grid-template-columns: repeat(3, 1fr); }
        }
        @media (max-width: 380px) {
            .input-grid { grid-template-columns: repeat(2, 1fr); }
        }
        .input-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px;
        }
        .input-label {
            color: #4da6ff;
            font-size: 1rem;
            margin-bottom: 6px;
            font-weight: 600;
            text-align: center;
            letter-spacing: 0.3px;
            text-shadow: 0 1px 1px rgba(0,0,0,0.5);
        }
        .input-field-container {
            position: relative;
            width: 80px;
            height: 36px;
        }
        .input-field {
            width: 100%;
            height: 100%;
            border: 1.5px solid #3a7fc5;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: bold;
            color: #ede1e1;
            background: rgba(20, 45, 80, 0.7);
            outline: none;
            transition: all 0.15s;
            text-align: center;
            line-height: 36px;
            padding: 0;
        }
        .input-field:focus {
            border-color: #2ecc71;
            transform: scale(1.02);
            box-shadow: 0 0 6px rgba(46,204,113,0.5);
        }
        .input-field.valid {
            background-color: #27ae60;
            border-color: #2ecc71;
            box-shadow: 0 0 8px rgba(46,204,113,0.6);
        }
        .button-container {
            display: flex;
            justify-content: center;
            gap: 12px;
            padding: 12px 0;
            width: 100%;
        }
        .btn {
            background: linear-gradient(to bottom, #3498db, #1a5a8c);
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            padding: 10px 20px;
            min-width: 100px;
            transition: all 0.15s;
            box-shadow: 0 2px 6px rgba(0,0,0,0.3);
            text-shadow: 0 1px 1px rgba(0,0,0,0.4);
            letter-spacing: 0.8px;
        }
        .btn.purple {
            background: linear-gradient(to bottom, #9b59b6, #6a11cb);
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.4);
        }
        .btn:active {
            transform: translateY(0);
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        @media (max-width: 450px) {
            .button-container {
                flex-wrap: wrap;
                gap: 8px;
            }
            .btn {
                min-width: 80px;
                padding: 8px 15px;
                font-size: 1.1rem;
            }
            .input-field-container {
                width: 70px;
                height: 32px;
            }
            .input-field {
                font-size: 0.9rem;
                line-height: 32px;
            }
            .input-label {
                font-size: 0.9rem;
            }
        }
    </style>
    <script src="/socket.io/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <div class="input-grid" id="input-container">
            <!-- 24个输入项由JavaScript生成 -->
        </div>
        <div class="button-container">
            <button class="btn purple" id="home-btn">首页</button>
            <button class="btn purple" id="next-btn">下页</button>
        </div>
    </div>

    <script>
    // 生成24个输入框
    function generateInputItems() {
        const container = document.getElementById('input-container');
        container.innerHTML = '';
        for (let i = 1; i <= 24; i++) {
            const inputItem = document.createElement('div');
            inputItem.className = 'input-item';
            
            const label = document.createElement('div');
            label.className = 'input-label';
            label.textContent = `数字${i}`;
            
            const fieldContainer = document.createElement('div');
            fieldContainer.className = 'input-field-container';
            
            const input = document.createElement('input');
            input.className = 'input-field';
            input.type = 'text';
            input.maxLength = 1;
            input.dataset.index = i;
            input.readOnly = true;
            
            fieldContainer.appendChild(input);
            inputItem.appendChild(label);
            inputItem.appendChild(fieldContainer);
            container.appendChild(inputItem);
        }
    }

    // 在文本框中显示布尔数据
    function displayBooleanData(booleanData) {
        const inputs = document.querySelectorAll('.input-field');
        inputs.forEach((input, index) => {
            if (index < booleanData.length) {
                const value = booleanData[index] ? 1 : 0;
                input.value = value;
                // 根据值设置样式
                if (value === 1) {
                    input.classList.add('valid');
                } else {
                    input.classList.remove('valid');
                }
            }
        });
    }

    // WebSocket连接和数据处理
    const socket = io();
    socket.on('dataUpdate', (data) => {
        if (data && data.page1Data) {
            // data.page1Data 现在是服务器处理好的布尔值数组
            displayBooleanData(data.page1Data);
        }
    });

    // 添加连接状态处理
    socket.on('connect', () => {
        console.log('WebSocket连接成功');
    });

    socket.on('disconnect', () => {
        console.log('WebSocket连接断开');
    });

    socket.on('connect_error', (error) => {
        console.error('连接错误:', error);
    });

    // 页面跳转
    document.getElementById('home-btn').onclick = function() {
        window.location.href = '0.html';
    };
    document.getElementById('next-btn').onclick = function() {
        window.location.href = '2.html';
    };

    // 页面加载时生成输入框
    generateInputItems();
    </script>
</body>
</html>