"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
var exception_1 = require("./exception");
exports.ExceptionRequestBody = exception_1.default;
exports.isExceptionRequestBody = exception_1.isExceptionRequestBody;
var read_coils_1 = require("./read-coils");
exports.ReadCoilsRequestBody = read_coils_1.default;
exports.isReadCoilsRequestBody = read_coils_1.isReadCoilsRequestBody;
var read_discrete_inputs_1 = require("./read-discrete-inputs");
exports.ReadDiscreteInputsRequestBody = read_discrete_inputs_1.default;
exports.isReadDiscreteInputsRequestBody = read_discrete_inputs_1.isReadDiscreteInputsRequestBody;
var read_holding_registers_1 = require("./read-holding-registers");
exports.ReadHoldingRegistersRequestBody = read_holding_registers_1.default;
exports.isReadHoldingRegistersRequestBody = read_holding_registers_1.isReadHoldingRegistersRequestBody;
var read_input_registers_1 = require("./read-input-registers");
exports.ReadInputRegistersRequestBody = read_input_registers_1.default;
exports.isReadInputRegistersRequestBody = read_input_registers_1.isReadInputRegistersRequestBody;
var request_body_1 = require("./request-body");
exports.ModbusRequestBody = request_body_1.default;
exports.isModbusRequestBody = request_body_1.isModbusRequestBody;
var request_factory_1 = require("./request-factory");
exports.RequestFactory = request_factory_1.default;
var write_multiple_coils_1 = require("./write-multiple-coils");
exports.WriteMultipleCoilsRequestBody = write_multiple_coils_1.default;
exports.isWriteMultipleCoilsRequestBody = write_multiple_coils_1.isWriteMultipleCoilsRequestBody;
var write_multiple_registers_1 = require("./write-multiple-registers");
exports.WriteMultipleRegistersRequestBody = write_multiple_registers_1.default;
exports.isWriteMultipleRegistersRequestBody = write_multiple_registers_1.isWriteMultipleRegistersRequestBody;
var write_single_coil_1 = require("./write-single-coil");
exports.WriteSingleCoilRequestBody = write_single_coil_1.default;
exports.isWriteSingleCoilRequestBody = write_single_coil_1.isWriteSingleCoilRequestBody;
var write_single_register_1 = require("./write-single-register");
exports.WriteSingleRegisterRequestBody = write_single_register_1.default;
exports.isWriteSingleRegisterRequestBody = write_single_register_1.isWriteSingleRegisterRequestBody;
