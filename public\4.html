<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>16个PT100输入</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        body {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 6px;
            background: linear-gradient(135deg, #0c1729 0%, #1a3e75 100%);
            touch-action: manipulation;
            color: #e0f2ff;
        }
        .container {
            width: 100%;
            max-width: 800px;
            padding: 12px;
            border-radius: 12px;
            backdrop-filter: blur(8px);
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin-bottom: 12px;
            width: 100%;
        }
        .input-item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 70px;
        }
        .input-row {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 7px;
            width: 100%;
        }
        .input-row label {
            font-weight: 600;
            font-size: 0.9rem;
            min-width: 32px;
            color: #4da6ff;
            text-align: right;
        }
        .input-row input {
            padding: 8px 5px;
            border: 1.5px solid #3a7fc5;
            border-radius: 6px;
            width: 100%;
            max-width: 110px;
            font-size: 0.9rem;
            text-align: center;
            background: transparent;
            color: #c8e4ff;
            min-height: 42px;
            touch-action: manipulation;
            transition: all 0.3s ease;
        }
        .input-row input:focus {
            border-color: #4dffb8;
            outline: none;
            box-shadow: 0 0 0 2px rgba(77, 255, 184, 0.5);
            background: transparent;
            transform: translateY(-2px);
        }
        .input-row span {
            font-weight: 500;
            min-width: 32px;
            color: #4da6ff;
            font-size: 0.9rem;
            text-align: left;
        }
        .error-hint {
            color: #ff6b6b;
            font-size: 0.75rem;
            font-weight: 500;
            height: 16px;
            margin-top: 3px;
            text-align: center;
            width: 100%;
        }
        .button-group {
            display: flex;
            justify-content: center;
            gap: 85px;
            margin-top: 4px;
            width: 100%;
            flex-wrap: wrap;
        }
        .btn {
            padding: 10px 14px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 600;
            min-width: 110px;
            min-height: 42px;
            touch-action: manipulation;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0,0,0,0.3);
        }
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 12px rgba(0,0,0,0.4);
        }
        .btn:active {
            transform: translateY(1px);
        }
        .btn-purple {
            background: linear-gradient(135deg, #9b59b6 0%, #6a11cb 100%);
            color: white;
        }
        /* 响应式设计 */
        @media (max-width: 900px) {
            .grid {
                grid-template-columns: repeat(4, 1fr);
                gap: 8px;
            }
        }
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: repeat(3, 1fr);
                gap: 10px;
            }
        }
        @media (max-width: 600px) {
            .container {
                padding: 10px;
            }
            .grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
            }
            .btn {
                padding: 8px 12px;
                font-size: 0.85rem;
                min-width: 90px;
                min-height: 38px;
            }
            .input-row input {
                max-width: 90px;
                font-size: 0.85rem;
            }
        }
        @media (max-width: 480px) {
            .grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 6px;
            }
            .input-row input {
                font-size: 0.8rem;
                min-height: 38px;
                max-width: 80px;
                padding: 7px 4px;
            }
            .input-row label {
                min-width: 32px;
                font-size: 0.85rem;
            }
            .input-row span {
                font-size: 0.85rem;
            }
            .button-group {
                gap: 6px;
            }
            .btn {
                min-width: 80px;
                padding: 7px 9px;
                font-size: 0.85rem;
            }
        }
    </style>
    <script src="/socket.io/socket.io.js"></script>
    <script>
        // 连接 WebSocket 并接收 server.js 分发的数据
        const socket = io();
        socket.on('dataUpdate', (data) => {
            // data.page4Data 现在是服务器处理好的、包含16个字符串的数组
            if (data && data.page4Data) {
                const decimalValues = data.page4Data;
                for (let i = 0; i < 16; i++) {
                    const input = document.getElementById(`pt${i + 1}`);
                    if (input && decimalValues[i] !== undefined) {
                        input.value = decimalValues[i];
                    }
                }
            }
        });

        // 添加连接状态处理
        socket.on('connect', () => {
            console.log('WebSocket连接成功');
        });

        socket.on('disconnect', () => {
            console.log('WebSocket连接断开');
        });

        socket.on('connect_error', (error) => {
            console.error('连接错误:', error);
        });
    </script>
</head>
<body>
    <div class="container">
        <div class="grid">
            <div class="input-item">
                <div class="input-row">
                    <label for="pt1">PT1</label>
                    <input type="text" id="pt1" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                <label for="pt2">PT2</label>
                <input type="text" id="pt2" readonly/>
                <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt3">PT3</label>
                    <input type="text" id="pt3" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt4">PT4</label>
                    <input type="text" id="pt4" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt5">PT5</label>
                    <input type="text" id="pt5" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt6">PT6</label>
                    <input type="text" id="pt6" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt7">PT7</label>
                    <input type="text" id="pt7" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt8">PT8</label>
                    <input type="text" id="pt8" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt9">PT9</label>
                    <input type="text" id="pt9" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt10">PT10</label>
                    <input type="text" id="pt10" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt11">PT11</label>
                    <input type="text" id="pt11" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt12">PT12</label>
                    <input type="text" id="pt12" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt13">PT13</label>
                    <input type="text" id="pt13" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt14">PT14</label>
                    <input type="text" id="pt14" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt15">PT15</label>
                    <input type="text" id="pt15" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
            <div class="input-item">
                <div class="input-row">
                    <label for="pt16">PT16</label>
                    <input type="text" id="pt16" readonly/>
                    <span>°C</span>
                </div>
                <div class="error-hint"></div>
            </div>
        </div>

        <div class="button-group">
            <button id="homeBtn" class="btn btn-purple">首页</button>
            <button id="prevBtn" class="btn btn-purple">上页</button>
        </div>
    </div>
    <script>
        // 按钮跳转逻辑
        document.getElementById('homeBtn').onclick = function() {
            window.location.href = '0.html';
        };
        document.getElementById('prevBtn').onclick = function() {
            window.location.href = '3.html';
        };
    </script>
</body>
</html>