// 引入所需模块
const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
const path = require('path');
const ModbusRTU = require('modbus-serial');
const client = new ModbusRTU();
const fs = require('fs');

// 创建 Express 应用和 HTTP 服务器
const app = express();
const server = http.createServer(app);
const io = new Server(server, { cors: { origin: '*' } }); // 启用跨域

// 读取配置文件
const config = JSON.parse(fs.readFileSync(path.join(__dirname, 'config.json'), 'utf-8'));
const plcIP = config.plcIP;
const plcPort = config.plcPort;
const plcSlaveId = config.plcSlaveId;
const pollInterval = config.pollInterval;
const startAddress = config.startAddress || 0;
const registerCount = config.registerCount || 35;

let isConnected = false; // 连接状态标志

// TCP连接函数，支持断线重连
function connectTCP() {
    client.connectTCP(plcIP, { port: plcPort })
        .then(() => {
            isConnected = true;
            console.log(`与PLC的TCP连接成功 (IP: ${plcIP}, 端口: ${plcPort})`);
            io.emit('plcStatus', { 
                connected: true,
                ip: plcIP,
                port: plcPort
            });
        })
        .catch(err => {
            isConnected = false;
            console.error('与PLC的TCP连接失败:', err.message);
            io.emit('plcStatus', { 
                connected: false, 
                error: 'PLC未连接'
            });
            setTimeout(connectTCP, 4000);
        });
}
connectTCP(); // 启动时自动连接

// 监听TCP关闭和错误，自动重连
client.on('close', () => {
    isConnected = false;
    console.log('与PLC的TCP连接已关闭，尝试重连...');
    io.emit('plcStatus', { 
        connected: false, 
        error: 'PLC未连接'
    });
    setTimeout(connectTCP, 4000);
});
client.on('error', err => {
    isConnected = false;
    console.error('PLC通信错误:', err.message);
    io.emit('plcStatus', { 
        connected: false, 
        error: 'PLC未连接'
    });
    setTimeout(connectTCP, 4000);
});

// 新增：解析数字量数据函数
// 将6个字节（48位）解析为两个包含24个布尔值（true/false）的数组
function parseDigitalData(bytes) {
    const inputs = [];
    const outputs = [];
    // 处理前3个字节（输入）
    for (let i = 0; i < 3; i++) {
        for (let j = 0; j < 8; j++) {
            // (bytes[i] >> j) & 1 会得到第j位的值 (0或1)
            // .toString(2) 会将字节转为二进制字符串
            inputs.push(!!((bytes[i] >> j) & 1));
        }
    }
    // 处理后3个字节（输出）
    for (let i = 3; i < 6; i++) {
        for (let j = 0; j < 8; j++) {
            outputs.push(!!((bytes[i] >> j) & 1));
        }
    }
    return { inputs, outputs };
}

// 静态文件服务，前端页面资源
app.use(express.static(path.join(__dirname, 'public')));
// 默认主页
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', '0.html'));
});

// 数据分发函数：将70字节原始数据分配给4个页面
function distributeDataToPages(data) {
    // 解析前6个字节的数字量
    const digitalData = parseDigitalData(data.slice(0, 6));
    
    // 解析模拟量输入 (字节6到37)
    const analogInputs = [];
    for (let i = 6; i < 38; i += 2) {
        const highByte = data[i];
        const lowByte = data[i+1];
        const value = (highByte << 8) | lowByte;
        analogInputs.push((value / 100).toFixed(2));
    }

    // 解析温度输入 (字节38到69)
    const temperatures = [];
    for (let i = 38; i < 70; i += 2) {
        const highByte = data[i];
        const lowByte = data[i+1];
        const value = (highByte << 8) | lowByte;
        temperatures.push((value / 10).toFixed(1));
    }

    return {
        page1Data: digitalData.inputs,     // 包含24个布尔值的数组
        page2Data: digitalData.outputs,    // 包含24个布尔值的数组
        page3Data: analogInputs,           // 包含16个模拟量字符串的数组
        page4Data: temperatures            // 包含16个温度字符串的数组
    };
}

// 定时读取Modbus数据并推送到前端
setInterval(async () => {
    if (!isConnected) return; // 未连接时跳过
    try {
        await client.setID(plcSlaveId); // 设置Modbus从站ID
        const data = await client.readHoldingRegisters(startAddress, registerCount); // 使用配置参数
        // 兼容不同库返回格式，取原始字节数组
        const rawBytes = data.buffer ? Array.from(new Uint8Array(data.buffer)) : data.data;
        const userData = distributeDataToPages(rawBytes); // 分发数据
        io.emit('dataUpdate', userData); // 推送到所有前端
    } catch (err) {
        console.error('Modbus读取失败:', err.message);
        io.emit('dataUpdate', null); // 读取失败时推送null
    }
}, pollInterval);

// Socket.IO连接处理
io.on('connection', (socket) => {
    console.log('新的WebSocket客户端连接');
    // 立即发送当前PLC状态
    socket.emit('plcStatus', { 
        connected: isConnected,
        ip: plcIP,
        port: plcPort
    });
});

// 启动HTTP服务器
const PORT = process.env.PORT || 3005;
server.listen(PORT, () => {
    console.log(`Server running on http://localhost:${PORT}`);
    console.log('Modbus RTU数据采集系统已启动');
    console.log('当前模式: 真实数据模式');
    console.log(`轮询频率: ${(1000 / pollInterval).toFixed(2)} Hz`);
});
