{"version": 3, "file": "user-request.d.ts", "sourceRoot": "", "sources": ["../src/user-request.ts"], "names": [], "mappings": ";AACA,OAAO,qBAAqB,MAAM,oBAAoB,CAAA;AACtD,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAA;AAC1D,OAAO,gBAAgB,MAAM,eAAe,CAAA;AAC5C,OAAO,gBAAgB,MAAM,eAAe,CAAA;AAC5C,OAAO,EAAE,gBAAgB,EAAE,MAAM,sBAAsB,CAAA;AACvD,OAAO,EAAE,kBAAkB,EAAE,MAAM,wBAAwB,CAAA;AAI3D,oBAAY,aAAa,GAAG,gBAAgB,GAAG,gBAAgB,CAAA;AAC/D,MAAM,WAAW,mBAAmB,CAAC,GAAG,SAAS,qBAAqB;IACpE,OAAO,EAAE,kBAAkB,CAAA;IAC3B,OAAO,EAAE,GAAG,CAAA;IACZ,QAAQ,EAAE,iBAAiB,CAAC,GAAG,CAAC,CAAA;CACjC;AAED,oBAAY,kBAAkB,CAAC,GAAG,SAAS,qBAAqB,IAAI,OAAO,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAA;AAUrG,MAAM,CAAC,OAAO,OAAO,WAAW,CAAC,GAAG,SAAS,qBAAqB,GAAG,GAAG;IACtE,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,GAAG,CAAA;IAChC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAA;IACnC,SAAS,CAAC,QAAQ,CAAC,QAAQ,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAA;IACpD,SAAS,CAAC,QAAQ,EAAG,CAAC,KAAK,EAAE,mBAAmB,CAAC,GAAG,CAAC,KAAK,IAAI,CAAA;IAC9D,SAAS,CAAC,OAAO,EAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,iBAAiB,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,KAAK,IAAI,CAAA;IAChF,SAAS,CAAC,MAAM,EAAG,MAAM,CAAC,OAAO,CAAA;IAEjC,SAAS,CAAC,QAAQ,EAAE,kBAAkB,CAAA;gBAQzB,OAAO,EAAE,GAAG,EAAE,OAAO,GAAE,MAAa;IAa1C,aAAa;IAIb,KAAK,CAAE,EAAE,EAAE,MAAM,IAAI;aAajB,OAAO;IAIX,IAAI;aAIP,OAAO;aAIP,OAAO;aAIP,OAAO;IAIJ,OAAO,CAAE,QAAQ,EAAE,iBAAiB,CAAC,GAAG,CAAC;aAU5C,MAAM;CAGX"}