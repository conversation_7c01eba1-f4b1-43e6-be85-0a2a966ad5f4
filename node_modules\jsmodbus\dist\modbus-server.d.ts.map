{"version": 3, "file": "modbus-server.d.ts", "sourceRoot": "", "sources": ["../src/modbus-server.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,YAAY,EAAE,MAAM,QAAQ,CAAA;AACrC,OAAO,EAAE,MAAM,EAAE,MAAM,KAAK,CAAA;AAC5B,OAAO,qBAAqB,MAAM,oBAAoB,CAAA;AACtD,OAAO,EAAE,YAAY,EAAE,MAAM,aAAa,CAAA;AAC1C,OAAO,kBAAkB,MAAM,wBAAwB,CAAA;AAEvD,aAAK,eAAe,GAAG,qBAAqB,CAAA;AAE5C,MAAM,WAAW,oBAAoB;IACnC,KAAK,EAAE,MAAM,CAAA;IACb,QAAQ,EAAE,MAAM,CAAA;IAChB,OAAO,EAAE,MAAM,CAAA;IACf,KAAK,EAAE,MAAM,CAAA;CACd;AASD,oBAAY,QAAQ,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,CAAA;AAE/C,MAAM,CAAC,OAAO,OAAO,YAAa,SAAQ,YAAY;IACpD,OAAO,CAAC,QAAQ,CAAsB;qBAC1B,MAAM;qBAGN,SAAS;qBAGT,QAAQ;qBAGR,MAAM;gBAIL,OAAO,GAAE,OAAO,CAAC,oBAAoB,CAAiC;aAS/E,KAAK;aAIL,QAAQ;aAIR,OAAO;aAIP,KAAK;IAIF,EAAE,CAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,KAAK,IAAI,GAAG,IAAI;IAC7F,EAAE,CAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACzF,EAAE,CAAE,KAAK,EAAE,cAAc,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IAC5F,EAAE,CAAE,KAAK,EAAE,eAAe,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IAC7F,EAAE,CAAE,KAAK,EAAE,oBAAoB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IAClG,EAAE,CAAE,KAAK,EAAE,uBAAuB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACrG,EAAE,CAAE,KAAK,EAAE,wBAAwB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACtG,EAAE,CAAE,KAAK,EAAE,sBAAsB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACpG,EAAE,CAAE,KAAK,EAAE,yBAAyB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACvG,EAAE,CAAE,KAAK,EAAE,0BAA0B,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACxG,EAAE,CAAE,KAAK,EAAE,oBAAoB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IAClG,EAAE,CAAE,KAAK,EAAE,uBAAuB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACrG,EAAE,CAAE,KAAK,EAAE,wBAAwB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACtG,EAAE,CAAE,KAAK,EAAE,iBAAiB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IAC/F,EAAE,CAAE,KAAK,EAAE,oBAAoB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IAClG,EAAE,CAAE,KAAK,EAAE,qBAAqB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACnG,EAAE,CAAE,KAAK,EAAE,qBAAqB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACnG,EAAE,CAAE,KAAK,EAAE,wBAAwB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACtG,EAAE,CAAE,KAAK,EAAE,yBAAyB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACvG,EAAE,CAAE,KAAK,EAAE,oBAAoB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IAClG,EAAE,CAAE,KAAK,EAAE,uBAAuB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACrG,EAAE,CAAE,KAAK,EAAE,oBAAoB,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,KAAK,IAAI,GAAG,IAAI;IAClG,EAAE,CAAE,KAAK,EAAE,wBAAwB,EAAE,QAAQ,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,KAAK,IAAI,GAAG,IAAI;IACtG,EAAE,CAAE,KAAK,EAAE,wBAAwB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACtG,EAAE,CAAE,KAAK,EAAE,wBAAwB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACtG,EAAE,CAAE,KAAK,EAAE,2BAA2B,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IACzG,EAAE,CAAE,KAAK,EAAE,wBAAwB,EAAE,QAAQ,EAAE,CAAC,gBAAgB,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI;IACxF,EAAE,CAAE,KAAK,EAAE,4BAA4B,EAAE,QAAQ,EAAE,CAAC,gBAAgB,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI;IAC5F,EAAE,CAAE,KAAK,EAAE,4BAA4B,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,KAAK,IAAI,GAAG,IAAI;IAC1G,EAAE,CAAE,KAAK,EAAE,YAAY,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,KAAK,IAAI,GAAG,IAAI;IAKlE,IAAI,CAAE,KAAK,EAAE,YAAY,EAAE,MAAM,EAAE,kBAAkB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,OAAO;IAC9E,IAAI,CAAE,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IAC1E,IAAI,CAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IAC7E,IAAI,CAAE,KAAK,EAAE,eAAe,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IAC9E,IAAI,CAAE,KAAK,EAAE,oBAAoB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACnF,IAAI,CAAE,KAAK,EAAE,uBAAuB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACtF,IAAI,CAAE,KAAK,EAAE,wBAAwB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACvF,IAAI,CAAE,KAAK,EAAE,sBAAsB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACrF,IAAI,CAAE,KAAK,EAAE,yBAAyB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACxF,IAAI,CAAE,KAAK,EAAE,0BAA0B,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACzF,IAAI,CAAE,KAAK,EAAE,oBAAoB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACnF,IAAI,CAAE,KAAK,EAAE,uBAAuB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACtF,IAAI,CAAE,KAAK,EAAE,wBAAwB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACvF,IAAI,CAAE,KAAK,EAAE,iBAAiB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IAChF,IAAI,CAAE,KAAK,EAAE,oBAAoB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACnF,IAAI,CAAE,KAAK,EAAE,qBAAqB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACpF,IAAI,CAAE,KAAK,EAAE,qBAAqB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACpF,IAAI,CAAE,KAAK,EAAE,wBAAwB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACvF,IAAI,CAAE,KAAK,EAAE,yBAAyB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACxF,IAAI,CAAE,KAAK,EAAE,oBAAoB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACnF,IAAI,CAAE,KAAK,EAAE,uBAAuB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACtF,IAAI,CAAE,KAAK,EAAE,oBAAoB,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,GAAG,OAAO;IACnF,IAAI,CAAE,KAAK,EAAE,wBAAwB,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,GAAG,OAAO;IACvF,IAAI,CAAE,KAAK,EAAE,wBAAwB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACvF,IAAI,CAAE,KAAK,EAAE,wBAAwB,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IACvF,IAAI,CAAE,KAAK,EAAE,2BAA2B,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;IAC1F,IAAI,CAAE,KAAK,EAAE,wBAAwB,EAAE,gBAAgB,EAAE,MAAM,GAAG,OAAO;IACzE,IAAI,CAAE,KAAK,EAAE,4BAA4B,EAAE,gBAAgB,EAAE,MAAM,GAAG,OAAO;IAC7E,IAAI,CAAE,KAAK,EAAE,4BAA4B,EAAE,OAAO,EAAE,eAAe,EAAE,EAAE,EAAE,QAAQ,GAAG,OAAO;CAKnG"}