{"name": "serialport", "version": "11.0.1", "description": "Node.js package to access serial ports. Linux, OSX and Windows. Welcome your robotic JavaScript overlords. Better yet, program them!", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc --build tsconfig-build.json"}, "repository": {"type": "git", "url": "git://github.com/serialport/node-serialport.git"}, "keywords": ["ccTalk", "com port", "COM", "data logging", "hardware", "iot", "johnny-five", "modem", "nodebots", "RFID", "robotics", "sensor", "serial port", "serial", "serialport", "sms gateway", "sms", "stream", "tty", "UART"], "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://www.roborooter.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://hipsterbrown.com/"}, {"name": "Maybe you? Come and help out!", "url": "https://github.com/node-serialport"}], "dependencies": {"@serialport/binding-mock": "10.2.2", "@serialport/bindings-cpp": "11.0.3", "@serialport/parser-byte-length": "11.0.1", "@serialport/parser-cctalk": "11.0.1", "@serialport/parser-delimiter": "11.0.1", "@serialport/parser-inter-byte-timeout": "11.0.1", "@serialport/parser-packet-length": "11.0.1", "@serialport/parser-readline": "11.0.1", "@serialport/parser-ready": "11.0.1", "@serialport/parser-regex": "11.0.1", "@serialport/parser-slip-encoder": "11.0.1", "@serialport/parser-spacepacket": "11.0.1", "@serialport/stream": "11.0.1", "debug": "4.3.4"}, "engines": {"node": ">=12.0.0"}, "license": "MIT", "funding": "https://opencollective.com/serialport/donate", "preferUnplugged": false, "devDependencies": {"typescript": "5.1.6"}, "gitHead": "12aeb531260f53d0adce42304db5074db85cc8d9"}