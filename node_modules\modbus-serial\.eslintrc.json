{"env": {"node": true, "es6": true}, "parserOptions": {"ecmaVersion": 2020}, "extends": "eslint:recommended", "rules": {"accessor-pairs": "error", "array-bracket-spacing": "off", "array-callback-return": "error", "arrow-body-style": "error", "arrow-parens": "error", "arrow-spacing": "error", "block-scoped-var": "off", "block-spacing": "off", "brace-style": "off", "callback-return": "off", "camelcase": "off", "capitalized-comments": "off", "class-methods-use-this": "error", "comma-dangle": "error", "comma-spacing": ["error", {"before": false, "after": true}], "comma-style": ["error", "last"], "complexity": "off", "computed-property-spacing": ["error", "never"], "consistent-return": "off", "consistent-this": "off", "curly": "off", "default-case": "off", "dot-location": ["error", "property"], "dot-notation": ["error", {"allowKeywords": true}], "eol-last": "error", "eqeqeq": "error", "func-call-spacing": "error", "func-name-matching": "error", "func-names": "off", "func-style": "off", "for-direction": "error", "generator-star-spacing": "error", "global-require": "off", "guard-for-in": "error", "handle-callback-err": "error", "id-blacklist": "error", "id-length": "off", "id-match": "error", "indent": ["error", 4, {"SwitchCase": 1, "MemberExpression": 1}], "init-declarations": "off", "jsx-quotes": "error", "key-spacing": "error", "keyword-spacing": "off", "line-comment-position": "off", "linebreak-style": ["off", "unix"], "lines-around-comment": "off", "lines-around-directive": "off", "max-depth": "error", "max-len": "off", "max-lines": "off", "max-nested-callbacks": "error", "max-params": "off", "max-statements": "off", "max-statements-per-line": "off", "multiline-ternary": "error", "new-cap": "error", "new-parens": "error", "newline-after-var": "off", "newline-before-return": "off", "newline-per-chained-call": "error", "no-alert": "error", "no-array-constructor": "error", "no-await-in-loop": "error", "no-bitwise": "off", "no-caller": "error", "no-catch-shadow": "error", "no-confusing-arrow": "error", "no-continue": "off", "no-div-regex": "error", "no-duplicate-imports": "error", "no-else-return": "error", "no-empty": ["error", {"allowEmptyCatch": true}], "no-empty-function": "error", "no-eq-null": "error", "no-eval": "error", "no-extend-native": "error", "no-extra-bind": "error", "no-extra-label": "error", "no-extra-parens": "off", "no-floating-decimal": "error", "no-implicit-coercion": "error", "no-implicit-globals": "off", "no-implied-eval": "error", "no-inline-comments": "off", "no-inner-declarations": ["error", "functions"], "no-invalid-this": "off", "no-iterator": "error", "no-label-var": "error", "no-labels": "error", "no-lone-blocks": "error", "no-lonely-if": "error", "no-loop-func": "error", "no-magic-numbers": "off", "no-mixed-operators": "off", "no-mixed-requires": "error", "no-multi-assign": "error", "no-multi-spaces": "off", "no-multi-str": "off", "no-multiple-empty-lines": "off", "no-native-reassign": "error", "no-negated-condition": "error", "no-negated-in-lhs": "error", "no-nested-ternary": "error", "no-new": "error", "no-new-func": "error", "no-new-object": "error", "no-new-require": "error", "no-new-wrappers": "error", "no-octal-escape": "error", "no-param-reassign": "off", "no-path-concat": "error", "no-plusplus": ["error", {"allowForLoopAfterthoughts": true}], "no-process-env": "error", "no-process-exit": "error", "no-proto": "error", "no-prototype-builtins": "error", "no-restricted-globals": "error", "no-restricted-imports": "error", "no-restricted-modules": "error", "no-restricted-properties": "error", "no-restricted-syntax": "error", "no-return-assign": "error", "no-return-await": "error", "no-script-url": "error", "no-self-compare": "error", "no-sequences": "error", "no-shadow": "off", "no-shadow-restricted-names": "error", "no-spaced-func": "error", "no-sync": "error", "no-tabs": "error", "no-template-curly-in-string": "error", "no-ternary": "error", "no-throw-literal": "error", "no-trailing-spaces": "error", "no-undef-init": "error", "no-undefined": "off", "no-underscore-dangle": "off", "no-unmodified-loop-condition": "error", "no-unneeded-ternary": "error", "no-unused-expressions": "off", "no-use-before-define": "off", "no-useless-call": "error", "no-useless-computed-key": "error", "no-useless-concat": "error", "no-useless-constructor": "error", "no-useless-escape": "error", "no-useless-rename": "error", "no-useless-return": "off", "no-var": "error", "no-void": "error", "no-warning-comments": "off", "no-whitespace-before-property": "error", "no-with": "error", "object-curly-newline": "error", "object-curly-spacing": ["error", "always"], "object-property-newline": ["error", {"allowMultiplePropertiesPerLine": true}], "object-shorthand": "off", "one-var": "off", "one-var-declaration-per-line": "error", "operator-assignment": "off", "operator-linebreak": "error", "padded-blocks": "off", "prefer-arrow-callback": "off", "prefer-const": "error", "prefer-destructuring": ["error", {"array": false, "object": false}], "prefer-numeric-literals": "error", "prefer-promise-reject-errors": "error", "prefer-reflect": "off", "prefer-rest-params": "error", "prefer-spread": "error", "prefer-template": "off", "quote-props": "off", "quotes": "error", "radix": ["error", "as-needed"], "require-await": "error", "require-jsdoc": "off", "rest-spread-spacing": "error", "semi": "error", "semi-spacing": "error", "sort-imports": "error", "sort-keys": "off", "sort-vars": "error", "space-before-blocks": "error", "space-before-function-paren": ["error", "never"], "space-in-parens": "error", "space-infix-ops": ["error", {"int32Hint": true}], "space-unary-ops": ["error", {"nonwords": false, "words": false}], "spaced-comment": "error", "strict": "off", "symbol-description": "error", "template-curly-spacing": "error", "template-tag-spacing": "error", "unicode-bom": ["error", "never"], "valid-jsdoc": "off", "vars-on-top": "off", "wrap-iife": "error", "wrap-regex": "error", "yield-star-spacing": "error", "yoda": ["error", "never"]}}