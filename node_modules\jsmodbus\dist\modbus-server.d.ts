/// <reference types="node" />
import { EventEmitter } from 'events';
import { Socket } from 'net';
import ModbusAbstractRequest from './abstract-request';
import { BooleanArray } from './constants';
import ModbusServerClient from './modbus-server-client';
declare type AbstractRequest = ModbusAbstractRequest;
export interface IModbusServerOptions {
    coils: Buffer;
    discrete: Buffer;
    holding: Buffer;
    input: Buffer;
}
export declare type BufferCB = (buffer: Buffer) => void;
export default class ModbusServer extends EventEmitter {
    private _options;
    private readonly _coils;
    private readonly _discrete;
    private readonly _holding;
    private readonly _input;
    constructor(options?: Partial<IModbusServerOptions>);
    readonly coils: Buffer;
    readonly discrete: Buffer;
    readonly holding: Buffer;
    readonly input: Buffer;
    on(event: 'connection', listener: (client: ModbusServerClient<any, any, any>) => void): this;
    on(event: 'readCoils', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'preReadCoils', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'postReadCoils', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'readDiscreteInputs', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'preReadDiscreteInputs', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'postReadDiscreteInputs', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'readHoldingRegisters', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'preReadHoldingRegisters', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'postReadHoldingRegisters', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'readInputRegisters', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'preReadInputRegisters', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'postReadInputRegisters', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'writeSingleCoil', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'preWriteSingleCoil', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'postWriteSingleCoil', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'writeSingleRegister', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'preWriteSingleRegister', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'postWriteSingleRegister', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'writeMultipleCoils', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'preWriteMultipleCoils', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'writeMultipleCoils', listener: (coils: Buffer, oldStatus: BooleanArray) => void): this;
    on(event: 'postWriteMultipleCoils', listener: (coils: Buffer, newStatus: BooleanArray) => void): this;
    on(event: 'postWriteMultipleCoils', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'writeMultipleRegisters', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'preWriteMultipleRegisters', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'writeMultipleRegisters', listener: (holdingRegisters: Buffer) => void): this;
    on(event: 'postWriteMultipleRegisters', listener: (holdingRegisters: Buffer) => void): this;
    on(event: 'postWriteMultipleRegisters', listener: (request: AbstractRequest, cb: BufferCB) => void): this;
    on(event: 'connection', listener: (socket: Socket) => void): this;
    emit(event: 'connection', client: ModbusServerClient<any, any, any>): boolean;
    emit(event: 'readCoils', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'preReadCoils', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'postReadCoils', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'readDiscreteInputs', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'preReadDiscreteInputs', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'postReadDiscreteInputs', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'readHoldingRegisters', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'preReadHoldingRegisters', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'postReadHoldingRegisters', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'readInputRegisters', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'preReadInputRegisters', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'postReadInputRegisters', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'writeSingleCoil', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'preWriteSingleCoil', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'postWriteSingleCoil', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'writeSingleRegister', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'preWriteSingleRegister', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'postWriteSingleRegister', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'writeMultipleCoils', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'preWriteMultipleCoils', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'writeMultipleCoils', coils: Buffer, oldStatus: BooleanArray): boolean;
    emit(event: 'postWriteMultipleCoils', coils: Buffer, newStatus: BooleanArray): boolean;
    emit(event: 'postWriteMultipleCoils', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'writeMultipleRegisters', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'preWriteMultipleRegisters', request: AbstractRequest, cb: BufferCB): boolean;
    emit(event: 'writeMultipleRegisters', holdingRegisters: Buffer): boolean;
    emit(event: 'postWriteMultipleRegisters', holdingRegisters: Buffer): boolean;
    emit(event: 'postWriteMultipleRegisters', request: AbstractRequest, cb: BufferCB): boolean;
}
export {};
//# sourceMappingURL=modbus-server.d.ts.map