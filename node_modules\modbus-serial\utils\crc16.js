"use strict";
/**
 * Calculates the buffers CRC16.
 *
 * @param {<PERSON><PERSON><PERSON>} buffer the data buffer.
 * @return {number} the calculated CRC16.
 */
module.exports = function crc16(buffer) {
    let crc = 0xFFFF;
    let odd;

    for (let i = 0; i < buffer.length; i++) {
        crc = crc ^ buffer[i];

        for (let j = 0; j < 8; j++) {
            odd = crc & 0x0001;
            crc = crc >> 1;
            if (odd) {
                crc = crc ^ 0xA001;
            }
        }
    }

    return crc;
};
